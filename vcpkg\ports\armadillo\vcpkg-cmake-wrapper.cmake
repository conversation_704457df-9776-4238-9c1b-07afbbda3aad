cmake_policy(PUSH)
cmake_policy(SET CMP0057 NEW)
if("CONFIG" IN_LIST ARGS OR "NO_MODULE" IN_LIST ARGS OR NOT "${ARGS};" MATCHES "^Armadillo;")
    _find_package(${ARGS})
else()
    list(REMOVE_AT ARGS 0)
    list(REMOVE_ITEM ARGS "MODULE")
    _find_package(ARMADILLO ${ARGS} NAMES Armadillo)
    if(ARMADILLO_FOUND)
        set(ARMADILLO_VERSION_STRING "${ARMADILLO_VERSION}")
        file(STRINGS "${CMAKE_CURRENT_LIST_DIR}/../../include/armadillo_bits/arma_version.hpp" ARMADILLO_VERSION_NAME REGEX "#define ARMA_VERSION_NAME ")
        string(REGEX REPLACE ".*#define ARMA_VERSION_NAME\ +\"([0-9a-zA-Z\ _-]+)\".*" "\\1" ARMADILLO_VERSION_NAME "${ARMADILLO_VERSION_NAME}")
    endif()
endif()
cmake_policy(POP)
