diff --git a/include/argon2.h b/include/argon2.h
index fc8682c..1401051 100644
--- a/include/argon2.h
+++ b/include/argon2.h
@@ -30,7 +30,10 @@ extern "C" {
 #ifdef A2_VISCTL
 #define ARGON2_PUBLIC __attribute__((visibility("default")))
 #define ARGON2_LOCAL __attribute__ ((visibility ("hidden")))
-#elif _MSC_VER
+#elif defined(_WIN32) && defined(USING_ARGON2_DLL)
+#define ARGON2_PUBLIC __declspec(dllimport)
+#define ARGON2_LOCAL
+#elif defined(_WIN32) && defined(BUILDING_ARGON2_DLL)
 #define ARGON2_PUBLIC __declspec(dllexport)
 #define ARGON2_LOCAL
 #else
