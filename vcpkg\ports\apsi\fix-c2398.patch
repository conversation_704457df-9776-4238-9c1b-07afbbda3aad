diff --git a/sender/apsi/util/cuckoo_filter.cpp b/sender/apsi/util/cuckoo_filter.cpp
index be5aead..e242796 100644
--- a/sender/apsi/util/cuckoo_filter.cpp
+++ b/sender/apsi/util/cuckoo_filter.cpp
@@ -246,8 +246,8 @@ CuckooFilter CuckooFilter::Load(istream &in, size_t &bytes_read)
 
     bytes_read = in_data.size();
     return CuckooFilter{ move(cuckoo_filter_table),
-                         cuckoo_filter_fbs->num_items(),
-                         cuckoo_filter_fbs->overflow()->index(),
+                         static_cast<size_t>(cuckoo_filter_fbs->num_items()),
+                         static_cast<size_t>(cuckoo_filter_fbs->overflow()->index()),
                          cuckoo_filter_fbs->overflow()->tag(),
                          cuckoo_filter_fbs->overflow()->used() };
 }
