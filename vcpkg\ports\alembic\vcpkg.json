{"name": "alembic", "version": "1.8.8", "description": "Alembic is an open framework for storing and sharing scene data that includes a C++ library, a file format, and client plugins and applications.", "homepage": "https://alembic.io/", "supports": "!(windows & x86) & !uwp", "dependencies": ["imath", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"hdf5": {"description": "Build with HDF5 support", "dependencies": ["hdf5"]}}}