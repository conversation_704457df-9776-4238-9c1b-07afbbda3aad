vcpkg_from_github(
    OUT_SOURCE_PATH SOURCE_PATH
    REPO Bromeon/Aurora
    REF c75699d2a8caa726260c29b6d7a0fd35f8f28933
    SHA512  862253e281eb441b773feb330d5ce4d7fe10769f516e927c0f875477ec912671fc35132a48986b2394f6d0a7160a5cb70afe868c597350c385ce23686b9613d6
    HEAD_REF master
)
file(INSTALL ${SOURCE_PATH}/include DESTINATION ${CURRENT_PACKAGES_DIR})
file(INSTALL ${SOURCE_PATH}/License.txt DESTINATION ${CURRENT_PACKAGES_DIR}/share/aurora RENAME copyright)
vcpkg_copy_pdbs()
