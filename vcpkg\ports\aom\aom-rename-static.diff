diff --git a/CMakeLists.txt b/CMakeLists.txt
index 2e5b623..bed61da 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -314,6 +314,15 @@ if(BUILD_SHARED_LIBS)
     set_target_properties(aom PROPERTIES SOVERSION ${SO_VERSION})
     set_target_properties(aom PROPERTIES VERSION ${SO_FILE_VERSION})
   endif()
+
+  # override conditional changes
+  set_target_properties(aom PROPERTIES
+      ARCHIVE_OUTPUT_NAME aom
+  )
+  set_target_properties(aom_static PROPERTIES
+      ARCHIVE_OUTPUT_NAME aom_static
+      EXCLUDE_FROM_ALL 1
+  )
 endif()
 
 if(NOT WIN32 AND NOT APPLE)
diff --git a/build/cmake/aom_install.cmake b/build/cmake/aom_install.cmake
index b02c7b9..c219841 100644
--- a/build/cmake/aom_install.cmake
+++ b/build/cmake/aom_install.cmake
@@ -79,7 +79,7 @@ macro(setup_aom_install_targets)
     endif()
 
     if(BUILD_SHARED_LIBS)
-      set(AOM_INSTALL_LIBS aom aom_static)
+      set(AOM_INSTALL_LIBS aom)
     else()
       set(AOM_INSTALL_LIBS aom)
     endif()
