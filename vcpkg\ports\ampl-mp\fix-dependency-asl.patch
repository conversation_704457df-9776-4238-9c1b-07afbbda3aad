diff --git a/CMakeLists.txt b/CMakeLists.txt
index 13f11cb..92089a3 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -323,9 +323,9 @@ check_cxx_source_compiles("
   endif()
 endif ()
 
+find_package(ampl-asl CONFIG REQUIRED)
 add_subdirectory(doc)
 add_subdirectory(src/amplsig)
-add_subdirectory(src/asl)
 add_subdirectory(src/cp)
 add_subdirectory(solvers)
 
diff --git a/src/cp/cp.cc b/src/cp/cp.cc
index d4adc35..ca8f35b 100644
--- a/src/cp/cp.cc
+++ b/src/cp/cp.cc
@@ -24,7 +24,7 @@
 #include <cstring>
 #include <limits>
 #include "mp/format.h"
-#include "funcadd.h"
+#include <asl/funcadd.h>
 
 namespace {
 void error(arglist *al, const char *message) {
