diff --git a/configs/azure_c_shared_utilityFunctions.cmake b/configs/azure_c_shared_utilityFunctions.cmake
index 6c2f415..e6133af 100644
--- a/configs/azure_c_shared_utilityFunctions.cmake
+++ b/configs/azure_c_shared_utilityFunctions.cmake
@@ -2,11 +2,11 @@
 #Licensed under the MIT license. See LICENSE file in the project root for full license information.
 
 function(target_link_libraries_with_arg_prefix arg_prefix whatIsBuilding lib)
-    if(${arg_prefix} STREQUAL "debug")
+    if(arg_prefix STREQUAL "debug")
         target_link_libraries(${whatIsBuilding} debug ${lib})
-    elseif(${arg_prefix} STREQUAL "optimized")
+    elseif(arg_prefix STREQUAL "optimized")
         target_link_libraries(${whatIsBuilding} optimized ${lib})
-    elseif(${arg_prefix} STREQUAL "general")
+    elseif(arg_prefix STREQUAL "general")
         target_link_libraries(${whatIsBuilding} general ${lib})
     else()
         target_link_libraries(${whatIsBuilding} ${lib})
@@ -43,13 +43,13 @@ function(windows_unittests_add_dll whatIsBuilding)
     set(ARG_PREFIX "none")
     foreach(f ${ARGN})
         set(skip_to_next FALSE)
-        if(${f} STREQUAL "ADDITIONAL_LIBS")
+        if(f STREQUAL "ADDITIONAL_LIBS")
             SET(PARSING_ADDITIONAL_LIBS ON)
             SET(PARSING_VALGRIND_SUPPRESSIONS_FILE OFF)
             set(ARG_PREFIX "none")
             #also unset all the other states
             set(skip_to_next TRUE)
-        elseif(${f} STREQUAL "VALGRIND_SUPPRESSIONS_FILE")
+        elseif(f STREQUAL "VALGRIND_SUPPRESSIONS_FILE")
             SET(PARSING_ADDITIONAL_LIBS OFF)
             SET(PARSING_VALGRIND_SUPPRESSIONS_FILE ON)
             set(skip_to_next TRUE)
@@ -57,7 +57,7 @@ function(windows_unittests_add_dll whatIsBuilding)
 
         if(NOT skip_to_next)
             if(PARSING_ADDITIONAL_LIBS)
-                if((${f} STREQUAL "debug") OR (${f} STREQUAL "optimized") OR (${f} STREQUAL "general"))
+                if((f STREQUAL "debug") OR (f STREQUAL "optimized") OR (f STREQUAL "general"))
                     SET(ARG_PREFIX ${f})
                 else()
                     target_link_libraries_with_arg_prefix(${ARG_PREFIX} ${whatIsBuilding}_dll ${f})
@@ -90,13 +90,13 @@ function(windows_unittests_add_exe whatIsBuilding)
     set(ARG_PREFIX "none")
     foreach(f ${ARGN})
         set(skip_to_next FALSE)
-        if(${f} STREQUAL "ADDITIONAL_LIBS")
+        if(f STREQUAL "ADDITIONAL_LIBS")
             SET(PARSING_ADDITIONAL_LIBS ON)
             SET(PARSING_VALGRIND_SUPPRESSIONS_FILE OFF)
             set(ARG_PREFIX "none")
             #also unset all the other states
             set(skip_to_next TRUE)
-        elseif(${f} STREQUAL "VALGRIND_SUPPRESSIONS_FILE")
+        elseif(f STREQUAL "VALGRIND_SUPPRESSIONS_FILE")
             SET(PARSING_ADDITIONAL_LIBS OFF)
             SET(PARSING_VALGRIND_SUPPRESSIONS_FILE ON)
             set(skip_to_next TRUE)
@@ -104,7 +104,7 @@ function(windows_unittests_add_exe whatIsBuilding)
 
         if(NOT skip_to_next)
             if(PARSING_ADDITIONAL_LIBS)
-                if((${f} STREQUAL "debug") OR (${f} STREQUAL "optimized") OR (${f} STREQUAL "general"))
+                if((f STREQUAL "debug") OR (f STREQUAL "optimized") OR (f STREQUAL "general"))
                     SET(ARG_PREFIX ${f})
                 else()
                     target_link_libraries_with_arg_prefix(${ARG_PREFIX} ${whatIsBuilding}_exe ${f})
@@ -150,14 +150,14 @@ function(linux_unittests_add_exe whatIsBuilding)
     set(ARG_PREFIX "none")
     foreach(f ${ARGN})
         set(skip_to_next FALSE)
-        if(${f} STREQUAL "ADDITIONAL_LIBS")
+        if(f STREQUAL "ADDITIONAL_LIBS")
             SET(PARSING_ADDITIONAL_LIBS ON)
             SET(PARSING_VALGRIND_SUPPRESSIONS_FILE OFF)
             set(ARG_PREFIX "none")
             set(skip_to_next TRUE)
             #also unset all the other states
 
-        elseif(${f} STREQUAL "VALGRIND_SUPPRESSIONS_FILE")
+        elseif(f STREQUAL "VALGRIND_SUPPRESSIONS_FILE")
             SET(PARSING_ADDITIONAL_LIBS OFF)
             SET(PARSING_VALGRIND_SUPPRESSIONS_FILE ON)
             set(skip_to_next TRUE)
@@ -165,7 +165,7 @@ function(linux_unittests_add_exe whatIsBuilding)
 
         if(NOT skip_to_next)
             if(PARSING_ADDITIONAL_LIBS)
-                if((${f} STREQUAL "debug") OR (${f} STREQUAL "optimized") OR (${f} STREQUAL "general"))
+                if((f STREQUAL "debug") OR (f STREQUAL "optimized") OR (f STREQUAL "general"))
                     SET(ARG_PREFIX ${f})
                 else()
                     target_link_libraries_with_arg_prefix(${ARG_PREFIX} ${whatIsBuilding}_exe ${f})
@@ -184,7 +184,7 @@ function(linux_unittests_add_exe whatIsBuilding)
 
     if(${run_valgrind})
         find_program(VALGRIND_FOUND NAMES valgrind)
-        if(${VALGRIND_FOUND} STREQUAL VALGRIND_FOUND-NOTFOUND)
+        if(VALGRIND_FOUND STREQUAL VALGRIND_FOUND-NOTFOUND)
             message(WARNING "run_valgrind was TRUE, but valgrind was not found - there will be no tests run under valgrind")
         else()
             add_test(NAME ${whatIsBuilding}_valgrind COMMAND valgrind                 --num-callers=100 --error-exitcode=1 --leak-check=full --track-origins=yes ${VALGRIND_SUPPRESSIONS_FILE_EXTRA_PARAMETER} $<TARGET_FILE:${whatIsBuilding}_exe>)
@@ -307,13 +307,13 @@ function(c_windows_unittests_add_dll whatIsBuilding folder)
     set(ARG_PREFIX "none")
     foreach(f ${ARGN})
         set(skip_to_next FALSE)
-        if(${f} STREQUAL "ADDITIONAL_LIBS")
+        if(f STREQUAL "ADDITIONAL_LIBS")
             SET(PARSING_ADDITIONAL_LIBS ON)
             SET(PARSING_VALGRIND_SUPPRESSIONS_FILE OFF)
             set(ARG_PREFIX "none")
             #also unset all the other states
             set(skip_to_next TRUE)
-        elseif(${f} STREQUAL "VALGRIND_SUPPRESSIONS_FILE")
+        elseif(f STREQUAL "VALGRIND_SUPPRESSIONS_FILE")
             SET(PARSING_ADDITIONAL_LIBS OFF)
             SET(PARSING_VALGRIND_SUPPRESSIONS_FILE ON)
             set(skip_to_next TRUE)
@@ -321,7 +321,7 @@ function(c_windows_unittests_add_dll whatIsBuilding folder)
 
         if(NOT skip_to_next)
             if(PARSING_ADDITIONAL_LIBS)
-                if((${f} STREQUAL "debug") OR (${f} STREQUAL "optimized") OR (${f} STREQUAL "general"))
+                if((f STREQUAL "debug") OR (f STREQUAL "optimized") OR (f STREQUAL "general"))
                     SET(ARG_PREFIX ${f})
                 else()
                     target_link_libraries_with_arg_prefix(${ARG_PREFIX} ${whatIsBuilding}_dll ${f})
@@ -369,13 +369,13 @@ function(c_windows_unittests_add_exe whatIsBuilding folder)
     set(ARG_PREFIX "none")
     foreach(f ${ARGN})
         set(skip_to_next FALSE)
-        if(${f} STREQUAL "ADDITIONAL_LIBS")
+        if(f STREQUAL "ADDITIONAL_LIBS")
             SET(PARSING_ADDITIONAL_LIBS ON)
             SET(PARSING_VALGRIND_SUPPRESSIONS_FILE OFF)
             set(ARG_PREFIX "none")
             #also unset all the other states
             set(skip_to_next TRUE)
-        elseif(${f} STREQUAL "VALGRIND_SUPPRESSIONS_FILE")
+        elseif(f STREQUAL "VALGRIND_SUPPRESSIONS_FILE")
             SET(PARSING_ADDITIONAL_LIBS OFF)
             SET(PARSING_VALGRIND_SUPPRESSIONS_FILE ON)
             set(skip_to_next TRUE)
@@ -383,7 +383,7 @@ function(c_windows_unittests_add_exe whatIsBuilding folder)
 
         if(NOT skip_to_next)
             if(PARSING_ADDITIONAL_LIBS)
-                if((${f} STREQUAL "debug") OR (${f} STREQUAL "optimized") OR (${f} STREQUAL "general"))
+                if((f STREQUAL "debug") OR (f STREQUAL "optimized") OR (f STREQUAL "general"))
                     SET(ARG_PREFIX ${f})
                 else()
                     target_link_libraries_with_arg_prefix(${ARG_PREFIX} ${whatIsBuilding}_exe ${f})
@@ -429,13 +429,13 @@ function(c_linux_unittests_add_exe whatIsBuilding folder)
     set(ARG_PREFIX "none")
     foreach(f ${ARGN})
         set(skip_to_next FALSE)
-        if(${f} STREQUAL "ADDITIONAL_LIBS")
+        if(f STREQUAL "ADDITIONAL_LIBS")
             SET(PARSING_ADDITIONAL_LIBS ON)
             SET(PARSING_VALGRIND_SUPPRESSIONS_FILE OFF)
             set(ARG_PREFIX "none")
             #also unset all the other states
             set(skip_to_next TRUE)
-        elseif(${f} STREQUAL "VALGRIND_SUPPRESSIONS_FILE")
+        elseif(f STREQUAL "VALGRIND_SUPPRESSIONS_FILE")
             SET(PARSING_ADDITIONAL_LIBS OFF)
             SET(PARSING_VALGRIND_SUPPRESSIONS_FILE ON)
             set(skip_to_next TRUE)
@@ -443,7 +443,7 @@ function(c_linux_unittests_add_exe whatIsBuilding folder)
 
         if(NOT skip_to_next)
             if(PARSING_ADDITIONAL_LIBS)
-                if((${f} STREQUAL "debug") OR (${f} STREQUAL "optimized") OR (${f} STREQUAL "general"))
+                if((f STREQUAL "debug") OR (f STREQUAL "optimized") OR (f STREQUAL "general"))
                     SET(ARG_PREFIX ${f})
                 else()
                     target_link_libraries_with_arg_prefix(${ARG_PREFIX} ${whatIsBuilding}_exe ${f})
@@ -462,7 +462,7 @@ function(c_linux_unittests_add_exe whatIsBuilding folder)
 
     if(${run_valgrind})
         find_program(VALGRIND_FOUND NAMES valgrind)
-        if(${VALGRIND_FOUND} STREQUAL VALGRIND_FOUND-NOTFOUND)
+        if(VALGRIND_FOUND STREQUAL VALGRIND_FOUND-NOTFOUND)
             message(WARNING "run_valgrind was TRUE, but valgrind was not found - there will be no tests run under valgrind")
         else()
             add_test(NAME ${whatIsBuilding}_valgrind COMMAND valgrind                 --gen-suppressions=all --num-callers=100 --error-exitcode=1 --leak-check=full --track-origins=yes ${VALGRIND_SUPPRESSIONS_FILE_EXTRA_PARAMETER} $<TARGET_FILE:${whatIsBuilding}_exe>)
@@ -546,29 +546,29 @@ function(compile_c_test_artifacts_as whatIsBuilding compileAsWhat)
             (("${whatIsBuilding}" MATCHES ".*int.*") AND ${run_int_tests})
         )
             if (${use_cppunittest})
-                if(${compileAsWhat} STREQUAL "C99")
+                if(compileAsWhat STREQUAL "C99")
                     compileTargetAsC99(${whatIsBuilding}_dll)
                     compileTargetAsC99(${whatIsBuilding}_testsonly_lib)
                 endif()
-                if(${compileAsWhat} STREQUAL "C11")
+                if(compileAsWhat STREQUAL "C11")
                     compileTargetAsC11(${whatIsBuilding}_dll)
                     compileTargetAsC11(${whatIsBuilding}_testsonly_lib)
                 endif()
             endif()
-            if(${compileAsWhat} STREQUAL "C99")
+            if(compileAsWhat STREQUAL "C99")
                 compileTargetAsC99(${whatIsBuilding}_exe)
             endif()
-            if(${compileAsWhat} STREQUAL "C11")
+            if(compileAsWhat STREQUAL "C11")
                 compileTargetAsC11(${whatIsBuilding}_exe)
             endif()
         else()
             if(
                 (("${whatIsBuilding}" MATCHES ".*e2e.*") AND ${nuget_e2e_tests})
             )
-                if(${compileAsWhat} STREQUAL "C99")
+                if(compileAsWhat STREQUAL "C99")
                     compileTargetAsC99(${whatIsBuilding}_exe)
                 endif()
-                if(${compileAsWhat} STREQUAL "C11")
+                if(compileAsWhat STREQUAL "C11")
                     compileTargetAsC11(${whatIsBuilding}_exe)
                 endif()
             else()
@@ -581,10 +581,10 @@ function(compile_c_test_artifacts_as whatIsBuilding compileAsWhat)
             (("${whatIsBuilding}" MATCHES ".*e2e.*") AND ${run_e2e_tests}) OR
             (("${whatIsBuilding}" MATCHES ".*int.*") AND ${run_int_tests})
         )
-            if(${compileAsWhat} STREQUAL "C99")
+            if(compileAsWhat STREQUAL "C99")
                 compileTargetAsC99(${whatIsBuilding}_exe)
             endif()
-            if(${compileAsWhat} STREQUAL "C11")
+            if(compileAsWhat STREQUAL "C11")
                 compileTargetAsC11(${whatIsBuilding}_exe)
             endif()
         endif()
@@ -641,15 +641,15 @@ function(set_platform_files c_shared_dir)
             set(CONDITION_C_FILE ${c_shared_dir}/adapters/condition_win32.c PARENT_SCOPE)
         endif()
         
-        if(${use_etw} STREQUAL "OFF")
+        if(use_etw STREQUAL "OFF")
             set(XLOGGING_C_FILE ${c_shared_dir}/src/xlogging.c PARENT_SCOPE)
             set(LOGGING_C_FILE ${c_shared_dir}/src/consolelogger.c PARENT_SCOPE)
             set(LOGGING_H_FILE ${c_shared_dir}/inc/azure_c_shared_utility/consolelogger.h PARENT_SCOPE)
-        elseif(${use_etw} STREQUAL "TRACELOGGING")
+        elseif(use_etw STREQUAL "TRACELOGGING")
             set(XLOGGING_C_FILE ${c_shared_dir}/src/etwxlogging.c PARENT_SCOPE)
             set(LOGGING_C_FILE ${c_shared_dir}/src/etwlogger_driver.c PARENT_SCOPE)
             set(LOGGING_H_FILE ${c_shared_dir}/inc/azure_c_shared_utility/etwlogger_driver.h PARENT_SCOPE)
-        elseif(${use_etw} STREQUAL "TRACELOGGING_WITH_CONSOLE")
+        elseif(use_etw STREQUAL "TRACELOGGING_WITH_CONSOLE")
             set(XLOGGING_C_FILE ${c_shared_dir}/src/etwxlogging.c PARENT_SCOPE)
             set(LOGGING_C_FILE ${c_shared_dir}/src/etwlogger_driver.c ${c_shared_dir}/src/consolelogger.c PARENT_SCOPE)
             set(LOGGING_H_FILE ${c_shared_dir}/inc/azure_c_shared_utility/etwlogger_driver.h ${c_shared_dir}/inc/azure_c_shared_utility/consolelogger.h PARENT_SCOPE)
