name: Check For Common Mistakes

on:
  pull_request:

jobs:
  Check:
    runs-on: ubuntu-22.04

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          # fetch-depth 50 tries to ensure we capture the whole history of the branch
          fetch-depth: 50

      - name: Bootstrap
        run: ./bootstrap-vcpkg.sh

      - name: Formatting
        run: |
          git config user.email github-actions
          git config user.name <EMAIL>

          git --version

          unset VCPKG_ROOT

          git diff --name-status --merge-base HEAD^ HEAD --diff-filter=MAR -- '*portfile.cmake' | sed 's/[MAR]\t*//' > .github-pr.changed-portfiles
          if [ -s .github-pr.changed-portfiles ]; then (grep -n -H -E '(vcpkg_apply_patches|vcpkg_build_msbuild|vcpkg_extract_source_archive_ex)' $(cat .github-pr.changed-portfiles) || true) > .github-pr.deprecated-function; else touch .github-pr.deprecated-function; fi
          if [ -s .github-pr.changed-portfiles ]; then (grep -n -H -E '(vcpkg_install_cmake|vcpkg_build_cmake|vcpkg_configure_cmake|vcpkg_fixup_cmake_targets)' $(cat .github-pr.changed-portfiles) || true) > .github-pr.deprecated-cmake; else touch .github-pr.deprecated-cmake; fi
          git diff --name-status --merge-base HEAD^ HEAD --diff-filter=MAR -- '*vcpkg.json' | sed 's/[MAR]\t*//' > .github-pr.changed-manifest-files
          cat .github-pr.changed-manifest-files | while read filename; do grep -q -E '"license": ' "$filename" || echo "$filename" || true; done > .github-pr.missing-license
          cat .github-pr.changed-manifest-files | while read filename; do grep -n -H '"license": "' "$filename" || true; done > .github-pr.all-licenses
          cat .github-pr.all-licenses | while read license; do \
            location=$(echo $license | grep -oP '^.*?:[0-9]+:'); \
            echo $license | \
            grep -oiP '(AGPL-1\.0|AGPL-3\.0|BSD-2-Clause-FreeBSD|BSD-2-Clause-NetBSD|bzip2-1\.0\.5|eCos-2\.0|GFDL-1\.1|GFDL-1\.2|GFDL-1\.3|GPL-1\.0|GPL-1\.0\+|GPL-2\.0|GPL-2\.0\+|GPL-2\.0-with-autoconf-exception|GPL-2\.0-with-bison-exception|GPL-2\.0-with-classpath-exception|GPL-2\.0-with-font-exception|GPL-2\.0-with-GCC-exception|GPL-3\.0|GPL-3\.0\+|GPL-3\.0-with-autoconf-exception|GPL-3\.0-with-GCC-exception|LGPL-2\.0|LGPL-2\.0\+|LGPL-2\.1|LGPL-2\.1\+|LGPL-3\.0|LGPL-3\.0\+|Nunit|StandardML-NJ|wxWindows)(?=[ ")&|,])' | \
            while read id; do \
              echo "$location$id"; \
            done || true; \
          done > .github-pr.deprecated-license
          ./vcpkg format-manifest --all --convert-control
          ./vcpkg format-feature-baseline scripts/ci.baseline.txt
          ./vcpkg format-feature-baseline scripts/ci.feature.baseline.txt
          git diff > .github-pr.format-manifest
          git add -u
          git commit -m "tmp" --allow-empty
          # HEAD^^ refers to the "main" commit that was merged into
          git checkout HEAD^^ -- versions
          git restore --staged versions
          ./vcpkg x-add-version --all --skip-formatting-check | grep 'instead of "version-string"' | tee .github-pr.version-string.out || true
          git checkout -- versions
          ./vcpkg x-add-version --all --skip-formatting-check --skip-version-format-check | tee .github-pr.x-add-version.out || true
          git diff > .github-pr.x-add-version.diff
          git reset HEAD~ --mixed

      - name: Generate Reply
        uses: actions/github-script@v7
        with:
          script: |
            const { promises: fs } = require('fs')
            const add_version = (await fs.readFile('.github-pr.x-add-version.diff', 'utf8')).trim()
            const add_version_out = (await fs.readFile('.github-pr.x-add-version.out', 'utf8')).trim()
            const version_string_out = (await fs.readFile('.github-pr.version-string.out', 'utf8')).trim()
            const format = (await fs.readFile('.github-pr.format-manifest', 'utf8')).trim()
            const deprecated_function = (await fs.readFile('.github-pr.deprecated-function', 'utf8')).split('\n').filter(s => s.length > 0)
            const deprecated_cmake = (await fs.readFile('.github-pr.deprecated-cmake', 'utf8')).split('\n').filter(s => s.length > 0)
            const missing_license = (await fs.readFile('.github-pr.missing-license', 'utf8')).trim()
            const deprecated_license = (await fs.readFile('.github-pr.deprecated-license', 'utf8')).split('\n').filter(s => s.length > 0)

            let approve = true;
            if (format !== "") {
              var format_output = '';
              format_output += "All vcpkg.json files and baselines must be formatted. To fix this problem, run:\n";
              format_output += "./vcpkg format-manifest ports/*/vcpkg.json\n";
              format_output += "./vcpkg format-feature-baseline scripts/ci.baseline.txt\n";
              format_output += "./vcpkg format-feature-baseline scripts/ci.feature.baseline.txt\n";
              format_output += "\n";
              format_output += "It should make the following changes:";
              format_output += "```diff\n" + format + "\n```";
              core.error(format_output);
              approve = false;
            }
            if (add_version_out !== "") {
              var add_version_output = '';
              add_version_output += "PRs must add only one version, and must not modify any published versions.\n";
              add_version_output += "When making any changes to a library, the version or port-version in vcpkg.json must be modified, and the version database updated.\n";
              add_version_output += "Making the following changes will fix this problem:";
              add_version_output += "```diff\n" + add_version_out + "\n```";
              core.error(add_version_output);
              approve = false;
            }
            if (version_string_out !== "") {
              core.warning(version_string_out);
            }
            if (add_version !== "") {
              var update_version_db_output = '';
              update_version_db_output += "After committing all other changes, the version database must be updated.\n";
              update_version_db_output += "This can be done by running the following commands after committing your changes:\n"
              update_version_db_output += "\n"
              update_version_db_output += "git add -u && git commit\n"
              update_version_db_output += "git checkout ${{ github.event.pull_request.base.sha }} -- versions\n"
              update_version_db_output += "./vcpkg x-add-version --all"
              core.error(update_version_db_output);
              approve = false;
            }
            
            if (deprecated_function.length > 0) {
              var deprecated_output = '';
              deprecated_output += "**You have modified or added at least one portfile where deprecated functions are used**\n"
              deprecated_output += "If you feel able to do so, please consider migrating them to the new functions.\n";
              core.warning(deprecated_output);

              let deprecated_functions = {
                  vcpkg_extract_source_archive_ex: 'vcpkg_extract_source_archive https://learn.microsoft.com/en-us/vcpkg/maintainers/functions/vcpkg_extract_source_archive',
                  vcpkg_build_msbuild: 'vcpkg_install_msbuild https://learn.microsoft.com/en-us/vcpkg/maintainers/functions/vcpkg_install_msbuild',
                  vcpkg_apply_patches: 'the PATCHES arguments to the \"extract\" helpers (for example, vcpkg_from_github() (https://learn.microsoft.com/en-us/vcpkg/maintainers/functions/vcpkg_from_github))',
              };
              for (let line of deprecated_function) {
                // line has the format: <path>:<line_number>:<matched_string
                // For example: ports/rtlsdr/portfile.cmake:42:    vcpkg_copy_tool_dependencies(${CURRENT_PACKAGES_DIR}/tools/${PORT})
                [file, line_number, match] = line.split(':');
                for (deprecated_func in deprecated_functions) {
                  if (match.indexOf(deprecated_func) !== -1) {
                    core.warning(`The function ${deprecated_func} is deprecated. Please use ${deprecated_functions[deprecated_func]}`, {file, startLine: line_number});
                    break;
                  }
                }
              }
            }

            if (deprecated_cmake.length > 0) {
              var deprecated_output = '';
              deprecated_output += "You have modified or added at least one portfile where deprecated functions are used.\n"
              deprecated_output += "These functions have been forbidden in vcpkg, please migrating them to the new functions.\n";
              deprecated_output += "In the ports that use the new function vcpkg_cmake_configure, vcpkg_cmake_install, vcpkg_cmake_build or vcpkg_cmake_config_fixup, you have to add the corresponding dependencies:\n";
              deprecated_output += "```json\n";
              deprecated_output += '{\n  "name": "vcpkg-cmake",\n  "host": true\n},\n'
              deprecated_output += '{\n  "name": "vcpkg-cmake-config",\n  "host": true\n}\n';
              deprecated_output += "```\n";
              core.error(deprecated_output);

              let deprecated_functions = {
                  vcpkg_install_cmake: 'vcpkg_cmake_install (from port vcpkg-cmake)',
                  vcpkg_install_cmake: 'vcpkg_cmake_install (from port vcpkg-cmake)',
                  vcpkg_build_cmake: 'vcpkg_cmake_build (from port vcpkg-cmake)',
                  vcpkg_configure_cmake: 'vcpkg_cmake_configure (Please remove the option PREFER_NINJA) (from port vcpkg-cmake)',
                  vcpkg_fixup_cmake_targets: 'vcpkg_cmake_config_fixup (from port vcpkg-cmake-config)',
              };
              for (let line of deprecated_cmake) {
                // line has the format: <path>:<line_number>:<matched_string
                // For example: ports/rtlsdr/portfile.cmake:42:    vcpkg_copy_tool_dependencies(${CURRENT_PACKAGES_DIR}/tools/${PORT})
                [file, line_number, match] = line.split(':');
                for (deprecated_cmake_function in deprecated_functions) {
                  if (match.indexOf(deprecated_cmake_function) !== -1) {
                    core.warning(`The function ${deprecated_cmake_function} is deprecated. Please use ${deprecated_functions[deprecated_cmake_function]}`, {file, startLine: line_number});
                    break;
                  }
                }
              }
            }

            if (missing_license !== "" || deprecated_license.length > 0) {
              core.summary.addRaw('You have modified or added at least one vcpkg.json where you should check the \"license\" field.');
              core.summary.addEOL();
            }

            if (missing_license !== "") {
              license_output = 'If you feel able to do so, please consider adding a "license" field to the following files:\n'
              license_output += missing_license
              license_output += "\n\nValid values for the license field can be found at https://learn.microsoft.com/vcpkg/reference/vcpkg-json#license\n\n"

              core.warning(license_output);
            }

            for (let line of deprecated_license) {
              [file, line_number, match] = line.split(':');
              var license_output = `SPDX deprecated license identifier ${match}.`;
              license_output += "\nIf you feel able to do so, please consider replacing it."
              license_output += "\nUpdated license identifiers can be found at https://spdx.org/licenses/."


              core.notice(license_output, {file, startLine: line_number});
            }

            core.summary.write();

            if (!approve) {
              process.exitCode = 1;
            }
