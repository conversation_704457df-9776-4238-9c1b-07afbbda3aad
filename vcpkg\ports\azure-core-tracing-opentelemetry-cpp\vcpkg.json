{"$comment": ["NOTE: All changes made to this file will get overwritten by the next port release.", "Please contribute your changes to https://github.com/Azure/azure-sdk-for-cpp."], "name": "azure-core-tracing-opentelemetry-cpp", "version-semver": "1.0.0-beta.4", "port-version": 5, "description": ["Microsoft Azure Core Tracing OpenTelemetry SDK for C++", "This library provides support for modern Azure SDK client libraries written in C++ to leverage OpenTelemetry APIs."], "homepage": "https://github.com/Azure/azure-sdk-for-cpp/tree/main/sdk/core/azure-core-tracing-opentelemetry", "license": "MIT", "supports": "!(windows & !static)", "dependencies": [{"name": "azure-core-cpp", "default-features": false, "version>=": "1.8.0"}, {"name": "opentelemetry-cpp", "version>=": "1.3.0"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}