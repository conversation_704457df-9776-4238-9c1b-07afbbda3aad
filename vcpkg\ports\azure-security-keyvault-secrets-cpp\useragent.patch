diff --git a/sdk/keyvault/azure-security-keyvault-secrets/CMakeLists.txt b/sdk/keyvault/azure-security-keyvault-secrets/CMakeLists.txt
index 280208205..e04413950 100644
--- a/sdk/keyvault/azure-security-keyvault-secrets/CMakeLists.txt
+++ b/sdk/keyvault/azure-security-keyvault-secrets/CMakeLists.txt
@@ -97,6 +97,8 @@ target_include_directories(
 
 target_link_libraries(azure-security-keyvault-secrets PUBLIC Azure::azure-core)
 
+target_compile_definitions(azure-security-keyvault-secrets PRIVATE _azure_BUILDING_SDK)
+
 # coverage. Has no effect if BUILD_CODE_COVERAGE is OFF
 create_code_coverage(keyvault azure-security-keyvault-secrets azure-security-keyvault-secrets-test "tests?/*;samples?/*")
 
