{"name": "aws-c-auth", "version": "0.9.0", "description": "C99 library implementation of AWS client-side authentication: standard credentials providers and signing.", "homepage": "https://github.com/awslabs/aws-c-auth", "license": "Apache-2.0", "dependencies": ["aws-c-cal", "aws-c-http", "aws-c-sdkutils", {"name": "s2n", "platform": "!uwp & !windows"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}