diff --git a/src/MessageTypeStore.cpp b/src/MessageTypeStore.cpp
index 382a138..5f09122 100644
--- a/src/MessageTypeStore.cpp
+++ b/src/MessageTypeStore.cpp
@@ -52,7 +52,7 @@ class ErrorCollector : public google::protobuf::compiler::MultiFileErrorCollecto
 public:
     ErrorCollector() : _error_count(0) { }
 
-    void AddError(const std::string& filename, int line, int column, const std::string& message) override
+    void RecordError(absl::string_view filename, int line, int column, absl::string_view message) override
     {
         _stream << "[" << filename << " (" << line << "," << column << ")] " << message << std::endl;
         _error_count++;
diff --git a/src/Socket_p.h b/src/Socket_p.h
index 9c3c084..9ccabda 100644
--- a/src/Socket_p.h
+++ b/src/Socket_p.h
@@ -548,7 +548,7 @@ namespace Arcus
 
         google::protobuf::io::ArrayInputStream array(wire_message->data, wire_message->size);
         google::protobuf::io::CodedInputStream stream(&array);
-        stream.SetTotalBytesLimit(message_size_maximum, message_size_warning);
+        stream.SetTotalBytesLimit(message_size_maximum);
         if(!message->ParseFromCodedStream(&stream))
         {
             error(ErrorCode::ParseFailedError, "Failed to parse message:" + std::string(wire_message->data));
