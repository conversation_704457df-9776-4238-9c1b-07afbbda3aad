diff --git a/cmake/external_dependencies.cmake b/cmake/external_dependencies.cmake
index acf16c0..3a49fb4 100644
--- a/cmake/external_dependencies.cmake
+++ b/cmake/external_dependencies.cmake
@@ -80,23 +80,12 @@ if(NOT NO_HTTP_CLIENT AND NOT USE_CRT_HTTP_CLIENT)
             set(BUILD_CURL 1)
             message(STATUS "  Building Curl as part of AWS SDK")
         else()
-            include(FindCURL)
+            find_package(CURL REQUIRED)
             if(NOT CURL_FOUND)
                 message(FATAL_ERROR "Could not find curl")
+            else()
+                set(CURL_LIBRARIES CURL::libcurl) 
             endif()
-
-            # When built from source using cmake, curl does not include
-            # CURL_INCLUDE_DIRS or CURL_INCLUDE_DIRS so we need to use
-            # find_package to fix it
-            if ("${CURL_INCLUDE_DIRS}" STREQUAL "" AND "${CURL_LIBRARIES}" STREQUAL "")
-                message(STATUS "Could not find curl include or library path, falling back to find with config.")
-                find_package(CURL)
-                set(CURL_LIBRARIES CURL::libcurl)
-            else ()
-                message(STATUS "  Curl include directory: ${CURL_INCLUDE_DIRS}")
-                List(APPEND EXTERNAL_DEPS_INCLUDE_DIRS ${CURL_INCLUDE_DIRS})
-                set(CLIENT_LIBS ${CURL_LIBRARIES})
-            endif ()
             set(CLIENT_LIBS_ABSTRACT_NAME curl)
             message(STATUS "  Curl target link: ${CURL_LIBRARIES}")
         endif()
