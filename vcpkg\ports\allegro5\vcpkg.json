{"name": "allegro5", "version": "********", "port-version": 1, "description": "Allegro is a cross-platform library mainly aimed at video game and multimedia programming. It handles common, low-level tasks such as creating windows, accepting user input, loading data, drawing images, playing sounds, etc. and generally abstracting away the underlying platform. However, Allegro is not a game engine: you are free to design and structure your program as you like.", "homepage": "https://liballeg.org/", "license": "BSD-3-Clause AND Zlib", "dependencies": [{"name": "alsa", "platform": "linux"}, "bzip2", {"name": "freetype", "default-features": false}, "libflac", "libjpeg-turbo", "libogg", "libpng", "libtheora", "libvorbis", {"name": "libwebp", "default-features": false}, "minimp3", "openal-soft", "opus", "opusfile", "physfs", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib"], "default-features": [{"name": "direct3d", "platform": "windows"}, {"name": "opengl", "platform": "!(arm & windows) & !uwp & !xbox"}], "features": {"direct3d": {"description": "Direct3D support", "supports": "windows"}, "opengl": {"description": "OpenGL support", "supports": "!(arm & windows) & !uwp & !xbox", "dependencies": ["opengl"]}}}