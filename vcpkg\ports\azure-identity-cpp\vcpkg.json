{"$comment": ["NOTE: All changes made to this file will get overwritten by the next port release.", "Please contribute your changes to https://github.com/Azure/azure-sdk-for-cpp."], "name": "azure-identity-cpp", "version-semver": "1.11.0", "description": ["Microsoft Azure Identity SDK for C++", "This library provides common authentication-related abstractions for Azure SDK."], "homepage": "https://github.com/Azure/azure-sdk-for-cpp/tree/main/sdk/identity/azure-identity", "license": "MIT", "dependencies": [{"name": "azure-core-cpp", "default-features": false, "version>=": "1.14.0"}, {"name": "openssl", "platform": "!windows & !uwp"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, {"name": "wil", "platform": "windows & !uwp"}]}