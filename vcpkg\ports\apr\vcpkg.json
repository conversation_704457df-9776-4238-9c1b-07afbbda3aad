{"name": "apr", "version": "1.7.6", "description": "The Apache Portable Runtime (APR) is a C library that forms a system portability layer that covers many operating systems.", "homepage": "https://apr.apache.org/", "license": "Apache-2.0", "supports": "!uwp & !mingw", "dependencies": [{"name": "apr", "host": true, "platform": "windows"}, {"name": "vcpkg-cmake", "host": true, "platform": "windows"}, {"name": "vcpkg-cmake-config", "host": true, "platform": "windows"}], "features": {"private-headers": {"description": "Install non-standard files required for building Apache httpd"}}}