diff --git a/configs/azure_iot_build_rules.cmake b/configs/azure_iot_build_rules.cmake
index 655b7f01..2f7e4ae4 100644
--- a/configs/azure_iot_build_rules.cmake
+++ b/configs/azure_iot_build_rules.cmake
@@ -71,8 +71,6 @@ if(MSVC)
       set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W4")
       set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} /W4")
 elseif(UNIX) #LINUX OR APPLE
-    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Werror")
-    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Werror")
     if(NOT (IN_OPENWRT OR APPLE))
         # _XOPEN_SOURCE=500 is required for glibc to expose random and srandom.
         set (CMAKE_C_FLAGS "-D_POSIX_C_SOURCE=200112L -D_XOPEN_SOURCE=500 ${CMAKE_C_FLAGS}")
@@ -208,12 +206,6 @@ endmacro(generate_cppunittest_wrapper)
 IF((WIN32) AND (NOT(MINGW)))
     #windows needs this define
     add_definitions(-D_CRT_SECURE_NO_WARNINGS)
-    # Make warning as error
-    add_definitions(/WX)
-ELSE()
-    # Make warning as error
-    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Werror")
-    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Werror")
 ENDIF()


