diff --git a/CMakeLists.txt b/CMakeLists.txt
index 1d4b65e..21b32ae 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -104,7 +104,7 @@ file(WRITE ${GENERATED_INCLUDE_DIR}/details.c "${DETAILS}")
 configure_file(${SRCDIR}/solvers/stdio1.h0 ${GENERATED_INCLUDE_DIR}/stdio1.h
                COPYONLY)
 
-if(CMAKE_CROSSCOMPILING AND CMAKE_SYSTEM_PROCESSOR MATCHES "^x86")
+if(0)
   include(CheckTypeSize)
   check_type_size(double DOUBLE_SIZE)
   check_type_size(long LONG_SIZE)
