diff --git a/CMakeLists.txt b/CMakeLists.txt
index 2828e64..7b84de3 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -40,6 +40,8 @@ endif()
 include("${CMAKE_CURRENT_SOURCE_DIR}/cmake/build_types/documentation.cmake")
 
 set(BOOST_VERSION 1.74)
+find_package(Boost ${BOOST_VERSION} REQUIRED)
+
 if(NOT INSTALLATION_ONLY)
     include("${CMAKE_CURRENT_SOURCE_DIR}/cmake/versioning/version.cmake")
     include("${CMAKE_CURRENT_SOURCE_DIR}/cmake/copyright_checker.cmake")
@@ -48,8 +50,6 @@ if(NOT INSTALLATION_ONLY)
     include("${CMAKE_CURRENT_SOURCE_DIR}/cmake/sanitizers.cmake")
     include("${CMAKE_CURRENT_SOURCE_DIR}/cmake/ccache.cmake")
 
-    find_package(Boost ${BOOST_VERSION} REQUIRED)
-
     find_package(span-lite QUIET)
     if (NOT span-lite_FOUND)
         message(WARNING "span-lite package not found, you will need to compile against C++20 or higher")
