diff --git a/CMakeLists.txt b/CMakeLists.txt
index d6136a8..9e260eb 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -76,7 +76,8 @@ set (pthreads_header pthread.h)
 set (flint2_header flint/flint.h)
 
 if(MSVC)
-    set(DEPS ${DEPS} pthreads)
+    find_package(pthreads REQUIRED)
+    set(PTHREADS_LIBRARIES PThreads4W::PThreads4W)
 else()
     option(CMAKE_THREAD_PREFER_PTHREAD "Prefer pthreads" yes)
     option(THREADS_PREFER_PTHREAD_FLAG "Prefer -pthread flag" yes)
