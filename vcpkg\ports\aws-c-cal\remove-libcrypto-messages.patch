diff --git a/cmake/modules/Findcrypto.cmake b/cmake/modules/Findcrypto.cmake
index fed83bb..9c1ae28 100644
--- a/cmake/modules/Findcrypto.cmake
+++ b/cmake/modules/Findcrypto.cmake
@@ -105,9 +105,6 @@ else()
         set(CRYPTO_FOUND true)
         set(crypto_FOUND true)
 
-        message(STATUS "LibCrypto Include Dir: ${crypto_INCLUDE_DIR}")
-        message(STATUS "LibCrypto Shared Lib:  ${crypto_SHARED_LIBRARY}")
-        message(STATUS "LibCrypto Static Lib:  ${crypto_STATIC_LIBRARY}")
         if (NOT TARGET AWS::crypto AND
             (EXISTS "${crypto_LIBRARY}")
             )
