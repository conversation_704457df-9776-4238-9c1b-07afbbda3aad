{"name": "ada-url", "version": "3.2.4", "description": "WHATWG-compliant and fast URL parser written in modern C++", "homepage": "https://ada-url.com/", "license": "MIT OR Apache-2.0", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"tools": {"description": "Build CLI tools (adaparse)", "supports": "!uwp", "dependencies": ["cxxopts", "fmt"]}}}