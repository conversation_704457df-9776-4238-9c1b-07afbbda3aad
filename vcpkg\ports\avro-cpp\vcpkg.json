{"name": "avro-cpp", "version": "1.12.0", "port-version": 2, "description": "Apache Avro is a data serialization system", "homepage": "https://github.com/apache/avro", "license": "Apache-2.0", "dependencies": ["boost-algorithm", "boost-any", "boost-crc", "boost-filesystem", "boost-format", "boost-iostreams", "boost-lexical-cast", "boost-math", "boost-program-options", "boost-random", "boost-tuple", "fmt", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zlib"], "features": {"snappy": {"description": "Support Snappy for compression", "dependencies": ["snappy"]}}}