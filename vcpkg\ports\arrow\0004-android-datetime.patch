diff --git a/cpp/src/arrow/vendored/datetime/tz.h b/cpp/src/arrow/vendored/datetime/tz.h
index 61ab3df106..d456d6765f 100644
--- a/cpp/src/arrow/vendored/datetime/tz.h
+++ b/cpp/src/arrow/vendored/datetime/tz.h
@@ -858,7 +858,9 @@ private:
     load_data(std::istream& inf, std::int32_t tzh_leapcnt, std::int32_t tzh_timecnt,
                                  std::int32_t tzh_typecnt, std::int32_t tzh_charcnt);
 # if defined(ANDROID) || defined(__ANDROID__)
+public:
     void parse_from_android_tzdata(std::ifstream& inf, const std::size_t off);
+private:
 # endif // defined(ANDROID) || defined(__ANDROID__)
 #else  // !USE_OS_TZDB
     DATE_API sys_info   get_info_impl(sys_seconds tp, int tz_int) const;
diff --git a/cpp/src/arrow/vendored/datetime/visibility.h b/cpp/src/arrow/vendored/datetime/visibility.h
index 780c00d70b..a9514edba7 100644
--- a/cpp/src/arrow/vendored/datetime/visibility.h
+++ b/cpp/src/arrow/vendored/datetime/visibility.h
@@ -21,6 +21,10 @@
 #  define USE_OS_TZDB 1
 #endif

+#if defined(ANDROID) || defined(__ANDROID__)
+#  define BUILD_TZ_LIB
+#endif
+
 #if defined(ARROW_STATIC)
 // intentially empty
 #elif defined(ARROW_EXPORTING)
