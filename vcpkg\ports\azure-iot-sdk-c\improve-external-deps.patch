diff --git a/CMakeLists.txt b/CMakeLists.txt
index 427e82e..5a58e47 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -257,6 +257,9 @@ add_subdirectory(${CMAKE_CURRENT_LIST_DIR}/deps)
 if(${use_installed_dependencies})
     find_package(parson REQUIRED CONFIG)
 endif()
+if(NOT TARGET parson)
+    add_library(parson ALIAS parson::parson)
+endif()
 
 include_directories(${MACRO_UTILS_INC_FOLDER})
 include_directories(${UMOCK_C_INC_FOLDER})
diff --git a/configs/azure_iot_sdksConfig.cmake b/configs/azure_iot_sdksConfig.cmake
index b9c62e8..edf5847 100644
--- a/configs/azure_iot_sdksConfig.cmake
+++ b/configs/azure_iot_sdksConfig.cmake
@@ -1,5 +1,11 @@
 #Copyright (c) Microsoft. All rights reserved.
 #Licensed under the MIT license. See LICENSE file in the project root for full license information.
+include(CMakeFindDependencyMacro)
+find_dependency(parson)
+find_dependency(uamqp)
+find_dependency(uhttp)
+find_dependency(umqtt)
+find_dependency(azure_c_shared_utility)
 
 include("${CMAKE_CURRENT_LIST_DIR}/azure_iot_sdksTargets.cmake")
 
diff --git a/iothub_client/tests/iothubclient_amqp_dt_e2e/CMakeLists.txt b/iothub_client/tests/iothubclient_amqp_dt_e2e/CMakeLists.txt
index 33b5b8709..45ef7b87a 100644
--- a/iothub_client/tests/iothubclient_amqp_dt_e2e/CMakeLists.txt
+++ b/iothub_client/tests/iothubclient_amqp_dt_e2e/CMakeLists.txt
@@ -22,11 +22,9 @@ set(${theseTestsName}_c_files
 
 set(${theseTestsName}_c_files
     ../../../certs/certs.c
-    ../../../deps/parson/parson.c
 )
 set(${theseTestsName}_h_files
     ../../../certs/certs.h
-    ../../../deps/parson/parson.h
     ../common_dt_e2e/iothubclient_common_dt_e2e.h
 )
 
diff --git a/iothub_client/tests/iothubclient_mqtt_dt_e2e_sfc/CMakeLists.txt b/iothub_client/tests/iothubclient_mqtt_dt_e2e_sfc/CMakeLists.txt
index d05d654..5872c8d 100644
--- a/iothub_client/tests/iothubclient_mqtt_dt_e2e_sfc/CMakeLists.txt
+++ b/iothub_client/tests/iothubclient_mqtt_dt_e2e_sfc/CMakeLists.txt
@@ -22,12 +22,10 @@ set(${theseTestsName}_nuget_c_files
 
 set(${theseTestsName}_c_files
     ../../../certs/certs.c
-    ../../../deps/parson/parson.c
 )
 
 set(${theseTestsName}_h_files
     ../../../certs/certs.h
-    ../../../deps/parson/parson.h
     ../common_dt_e2e/iothubclient_common_dt_e2e.h
 )
 
