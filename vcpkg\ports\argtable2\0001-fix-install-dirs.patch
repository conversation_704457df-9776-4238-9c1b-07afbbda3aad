From 6594abbd953d0e26105fd6c982990eb702f27804 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Tue, 22 Sep 2020 15:22:00 -0700
Subject: [PATCH 1/2] fix install dirs

---
 src/CMakeLists.txt | 11 +++++++----
 1 file changed, 7 insertions(+), 4 deletions(-)

diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 773c4c8..03e112c 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -1,5 +1,4 @@
-# for now, install in subdirectory of source directory by default
-SET( CMAKE_INSTALL_PREFIX .. )
+include(GNUInstallDirs)
 
 IF( WIN32 )
 	ADD_LIBRARY( argtable2 arg_dbl.c arg_end.c arg_file.c arg_int.c arg_lit.c arg_rem.c arg_str.c argtable2.c argtable2.h getopt.c getopt.h getopt1.c )
@@ -7,5 +6,9 @@ ELSE( WIN32 )
 	ADD_LIBRARY( argtable2 arg_date.c arg_dbl.c arg_end.c arg_file.c arg_int.c arg_lit.c arg_rem.c arg_rex.c arg_str.c argtable2.c argtable2.h getopt.c getopt.h getopt1.c )
 ENDIF( WIN32 )
 
-INSTALL( TARGETS argtable2 ARCHIVE DESTINATION lib)
-SET_TARGET_PROPERTIES( argtable2 PROPERTIES DEBUG_POSTFIX d )
+INSTALL(TARGETS argtable2
+        RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
+        LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
+        ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
+)
+INSTALL(FILES argtable2.h DESTINATION ${CMAKE_INSTALL_INCLUDEDIR})
-- 
2.24.3 (Apple Git-128)

