name: 'Close stale issues and PRs'
on:
  schedule:
    - cron: '30 1 * * *'

permissions:
  issues: write

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v8
        with:
          stale-issue-message: 'This is an automated message. Per our repo policy, stale issues get closed if there has been no activity in the past 28 days. The issue will be automatically closed in 14 days. If you wish to keep this issue open, please add a new comment.'
          any-of-labels: 'category:question,requires:repro,requires:more-information'
          days-before-issue-stale: 28
          days-before-pr-stale: -1
          days-before-close: 14
      - uses: actions/stale@v8
        with:
          stale-issue-message: "This is an automated message. Per our repo policy, stale issues get closed if there has been no activity in the past 60 days. The issue will be automatically closed in 14 days. If you wish to keep this issue open, please add a new comment."
          any-of-labels: 'category:new-port'
          close-issue-label: 'info:new-port-unresolved'
          days-before-issue-stale: 60
          days-before-pr-stale: -1
          days-before-close: 14
      - uses: actions/stale@v8
        with:
          stale-issue-message: "This is an automated message. Per our repo policy, stale issues get closed if there has been no activity in the past 180 days. The issue will be automatically closed in 14 days. If you wish to keep this issue open, please add a new comment."
          exempt-issue-labels: 'no-stale,category:new-port,category:question,requires:repro,requires:more-information'
          days-before-issue-stale: 180
          days-before-pr-stale: -1
          days-before-close: 14
          operations-per-run: 50
