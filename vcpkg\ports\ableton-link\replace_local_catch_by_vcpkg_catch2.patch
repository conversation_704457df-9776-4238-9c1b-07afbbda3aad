--- ableton-link.orig/cmake_include/CatchConfig.cmake
+++ ableton-link/cmake_include/CatchConfig.cmake
@@ -1,6 +1,12 @@
-add_library(Catch::Catch IMPORTED INTERFACE)
+if (NEED_CATCH2 MATCHES "ON") 
+  add_library(Catch::Catch IMPORTED INTERFACE)
 
-set_property(TARGET Catch::Catch APPEND PROPERTY
-  INTERFACE_INCLUDE_DIRECTORIES
-  ${CMAKE_CURRENT_LIST_DIR}/../third_party/catch
-)
+  find_package(Catch2 CONFIG REQUIRED)
+  if(Catch2_FOUND)
+    message(STATUS "Dependency Catch2 found in ${Catch2_DIR}")  
+    set_property(TARGET Catch::Catch APPEND PROPERTY
+      INTERFACE_INCLUDE_DIRECTORIES
+      ${Catch2_DIR}/../../include
+    )
+  endif()
+endif()
 

--- "a/src/CMakeLists.txt"
+++ "b/src/CMakeLists.txt"
@@ -77,4 +77,9 @@ function(configure_link_test_executable target)
 function(configure_link_test_executable target)
-  target_link_libraries(${target} Catch::Catch Ableton::Link)
+  if (NEED_CATCH2 MATCHES "ON")
+    target_link_libraries(${target} Catch2::Catch2WithMain Ableton::Link)
   target_compile_definitions(${target} PRIVATE -DCATCH_CONFIG_ENABLE_BENCHMARKING=1)
+  else()
+    target_link_libraries(${target} Ableton::Link)
+    target_compile_definitions(${target} PRIVATE -DCATCH_CONFIG_ENABLE_BENCHMARKING=0) 
+  endif()
 endfunction()
 

--- "a/src/ableton/link/tst_LinearRegression.cpp"
+++ "b/src/ableton/link/tst_LinearRegression.cpp"
@@ -22,6 +22,8 @@
 #include <array>
 #include <vector>
 
+using Catch::Approx;
+
 namespace ableton
 {
 namespace link
 

--- "a/src/ableton/link/tst_Beats.cpp"
+++ "b/src/ableton/link/tst_Beats.cpp"
@@ -20,6 +20,8 @@
 #include <ableton/link/Beats.hpp>
 #include <ableton/test/CatchWrapper.hpp>
 
+using Catch::Approx;
+
 namespace ableton
 {
 namespace link
 

--- "a/src/ableton/link/tst_Median.cpp"
+++ "b/src/ableton/link/tst_Median.cpp"
@@ -22,6 +22,8 @@
 #include <array>
 #include <vector>
 
+using Catch::Approx;
+
 namespace ableton
 {
 namespace link
 

--- "a/src/ableton/link/tst_Tempo.cpp"
+++ "b/src/ableton/link/tst_Tempo.cpp"
@@ -20,6 +20,8 @@
 #include <ableton/link/Tempo.hpp>
 #include <ableton/test/CatchWrapper.hpp>
 
+using Catch::Approx;
+
 namespace ableton
 {
 namespace link
 
