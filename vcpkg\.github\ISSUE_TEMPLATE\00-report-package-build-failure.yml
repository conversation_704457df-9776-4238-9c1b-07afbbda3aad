name: Report package build failure
description: Let us know about build failures in ports.
title: "[<port name>] build failure"
labels: ["category:port-bug"]
body:
  - type: markdown
    attributes:
      value: | 
        ⚠ Please use the generated issue template instead to report a port build failure. ⚠ <br>
        See [here](https://github.com/microsoft/vcpkg/issues/30604) how to properly report an issue with the latest vcpkg version:
        <video width="800" controls="controls" muted="muted" src="https://user-images.githubusercontent.com/41973254/229313721-b82c2d3d-b0f9-4e31-98df-08d75831f695.mp4"></video>
  - type: input
    id: os
    attributes:
      label: Operating system
      placeholder: "e.g. Windows/Linux etc..."
    validations:
      required: true
  - type: input
    id: compiler
    attributes:
      label: Compiler
      placeholder: "GCC, Clang, MSVC, MinGW, ..."
    validations:
      required: false
  - type: textarea
    id: reproduce
    attributes:
      label: Steps to reproduce the behavior
      placeholder: "`./vcpkg install xxxx`"
      render: "Shell"
    validations:
      required: true
  - type: textarea
    id: logs
    attributes:
      label: Failure logs
      description: The console output and all the logs mentioned in the output.
    validations:
      required: true
  - type: textarea
    id: context
    attributes:
      label: Additional context
      description: Add any other context about the problem here, such as what you have already tried to resolve the issue.
    validations:
      required: false
