{"name": "arrayfire", "version-semver": "3.8.0", "port-version": 8, "description": "ArrayFire is a general-purpose library that simplifies the process of developing software that targets parallel and massively-parallel architectures including CPUs, GPUs, and other hardware acceleration devices.", "homepage": "https://github.com/arrayfire/arrayfire", "license": "BSD-3-<PERSON><PERSON>", "supports": "x64", "dependencies": ["boost-compute", "boost-stacktrace", "glad", "spdlog", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "default-features": ["cpu", "unified"], "features": {"cpu": {"description": "ArrayFire CPU backend", "dependencies": ["intel-mkl"]}, "cuda": {"description": "ArrayFire CUDA backend. This feature only supports dynamic because dependency cudnn is dynamic only.", "dependencies": ["cuda", "cudnn"]}, "opencl": {"description": "ArrayFire OpenCL backend", "dependencies": ["clblast", "clfft", "opencl"]}, "unified": {"description": "ArrayFire unified backend", "dependencies": ["intel-mkl"]}}}