APR provides CMake targets whose names will start with the 'lib' prefix in shared configurations, while static configurations will not:

  find_package(apr CONFIG REQUIRED)

  # Use the shared configuration
  target_link_libraries(main PRIVATE apr::apr-1 apr::libaprapp-1)

  # Use the static configuration
  target_link_libraries(main PRIVATE apr::apr-1 apr::aprapp-1)

To ensure compatibility with both static and shared configurations:

  find_package(apr CONFIG REQUIRED)
  target_link_libraries(main PRIVATE
    $<$<TARGET_EXISTS:apr::apr-1>:apr::apr-1>
    $<$<TARGET_EXISTS:apr::aprapp-1>:apr::aprapp-1>
    $<$<TARGET_EXISTS:apr::libapr-1>:apr::libapr-1>
    $<$<TARGET_EXISTS:apr::libaprapp-1>:apr::libaprapp-1>
  )
